{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -p tsconfig.build.json && vite build", "lint": "biome check --apply-unsafe --no-errors-on-unmatched --files-ignore-unknown=true ./", "preview": "vite preview", "generate-client": "openapi-ts"}, "dependencies": {"@chakra-ui/react": "^3.8.0", "@emotion/react": "^11.14.0", "@tanstack/react-query": "^5.28.14", "@tanstack/react-query-devtools": "^5.74.9", "@tanstack/react-router": "1.19.1", "axios": "1.9.0", "form-data": "4.0.2", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^5.0.0", "react-hook-form": "7.49.3", "react-icons": "^5.5.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@hey-api/openapi-ts": "^0.57.0", "@playwright/test": "^1.52.0", "@tanstack/router-devtools": "1.19.1", "@tanstack/router-vite-plugin": "1.19.0", "@types/node": "^22.15.3", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react-swc": "^3.9.0", "dotenv": "^16.4.5", "typescript": "^5.2.2", "vite": "^6.3.4"}}